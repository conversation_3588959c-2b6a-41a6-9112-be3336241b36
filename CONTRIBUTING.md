# Contributing to Renaissance Periodisation Training API

Thank you for your interest in contributing to the RP Training API! This document provides guidelines and information for contributors.

## 🎯 Project Vision

We're building an evidence-based hypertrophy training platform that implements Renaissance Periodisation principles with scientific rigour and clean architecture. Every contribution should align with these core values:

- **Scientific Accuracy** - All training algorithms must reflect published RP methodology
- **Code Quality** - Maintain high standards with comprehensive testing and documentation
- **User Experience** - Prioritise intuitive, efficient API design for mobile-first applications
- **Maintainability** - Follow Clean Architecture principles and SOLID design patterns

## 🚀 Getting Started

### Prerequisites

- **Python 3.11+** with type hints knowledge
- **Docker & Docker Compose** for development environment
- **Git** with conventional commit message experience
- **PostgreSQL** understanding for database contributions
- **FastAPI** familiarity for API development

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/rp-training-api.git
   cd rp-training-api
   ```

2. **Set Up Environment**
   ```bash
   python3.11 -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   pip install -r requirements/dev.txt
   ```

3. **Start Development Services**
   ```bash
   cd docker
   docker-compose up -d
   ```

4. **Run Tests**
   ```bash
   pytest --cov=app
   pre-commit run --all-files
   ```

## 📋 Contribution Types

### 🐛 Bug Reports

**Before submitting:**
- Search existing issues to avoid duplicates
- Test against the latest development version
- Gather relevant system information

**Include in your report:**
- Clear, descriptive title
- Steps to reproduce the issue
- Expected vs actual behaviour
- Environment details (Python version, OS, etc.)
- Relevant logs or error messages

### 💡 Feature Requests

**Before proposing:**
- Check the [Master PRD](docs/master_prd.md) for planned features
- Ensure alignment with RP scientific principles
- Consider the mobile-first API design philosophy

**Include in your proposal:**
- Clear problem statement
- Proposed solution with technical approach
- Consideration of existing architecture
- Potential impact on performance and maintainability

### 🔧 Code Contributions

**Development Workflow:**

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/descriptive-name
   ```

2. **Follow Architecture Patterns**
   - Respect Clean Architecture layer boundaries
   - Use dependency injection throughout
   - Implement repository pattern for data access
   - Write comprehensive unit and integration tests

3. **Code Quality Standards**
   ```bash
   # Format code
   black .
   isort .
   
   # Type checking and linting
   mypy .
   flake8 .
   
   # Run tests with coverage
   pytest --cov=app --cov-fail-under=90
   ```

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add exercise search functionality"
   ```

5. **Submit Pull Request**
   - Clear title and description
   - Link related issues
   - Include testing evidence
   - Request appropriate reviewers

## 🧪 Testing Requirements

### Test Categories

- **Unit Tests** - Domain logic, algorithms, and individual components
- **Integration Tests** - API endpoints, database operations, and service interactions
- **Behavioural Tests** - User scenarios using Gherkin syntax
- **Performance Tests** - Response times and resource usage

### Coverage Requirements

- **Minimum Overall Coverage**: 90%
- **Critical Path Coverage**: 100% (authentication, RP algorithms)
- **New Feature Coverage**: 95%
- **Integration Test Coverage**: All API endpoints

### Test Structure

```python
# Example test structure
def test_mev_calculation_with_valid_feedback():
    """Test MEV calculation with valid user feedback scores."""
    # Arrange
    feedback = MEVFeedback(
        mind_muscle_connection=2,
        pump_rating=1,
        muscle_disruption=2
    )
    
    # Act
    result = mev_calculator.calculate_recommendation(feedback)
    
    # Assert
    assert result.recommendation == MEVRecommendation.PROGRESS_NORMALLY
    assert result.confidence_score > 0.8
```

## 📝 Documentation Standards

### Code Documentation

- **Docstrings** - All public functions, classes, and modules
- **Type Hints** - Complete type annotations for all functions
- **Inline Comments** - Complex business logic and algorithm explanations
- **API Documentation** - OpenAPI schema with examples

### Documentation Style

```python
def calculate_stimulus_to_fatigue_ratio(
    raw_stimulus: int,
    fatigue_score: int,
    exercise_type: ExerciseType
) -> StimulusFatigueRatio:
    """Calculate the stimulus-to-fatigue ratio for exercise effectiveness.
    
    Implements the RP methodology for evaluating exercise effectiveness
    based on stimulus magnitude relative to fatigue cost.
    
    Args:
        raw_stimulus: Raw stimulus magnitude (0-10 scale)
        fatigue_score: Perceived fatigue cost (0-10 scale)  
        exercise_type: Type of exercise for context weighting
        
    Returns:
        StimulusFatigueRatio: Calculated ratio with effectiveness rating
        
    Raises:
        ValueError: If stimulus or fatigue scores are outside valid range
        
    Example:
        >>> ratio = calculate_stimulus_to_fatigue_ratio(8, 4, ExerciseType.COMPOUND)
        >>> ratio.value
        2.0
        >>> ratio.effectiveness
        EffectivenessRating.HIGH
    """
```

## 🎨 Code Style Guidelines

### Python Style

- **PEP 8** compliance with Black formatting
- **British English** spelling throughout (colour, optimise, etc.)
- **Descriptive naming** - prefer clarity over brevity
- **Type hints** - use modern Python 3.11+ syntax

### Architecture Patterns

- **Clean Architecture** - strict layer separation
- **Dependency Injection** - constructor injection preferred
- **Repository Pattern** - abstract data access
- **Domain-Driven Design** - rich domain models

### API Design

- **RESTful principles** - proper HTTP methods and status codes
- **Consistent naming** - snake_case for JSON, PascalCase for classes
- **Comprehensive validation** - Pydantic schemas for all inputs
- **Mobile-optimised** - efficient payloads and response structures

## 🔄 Pull Request Process

### Before Submitting

1. **Rebase on latest main**
   ```bash
   git fetch origin
   git rebase origin/main
   ```

2. **Run complete test suite**
   ```bash
   pytest --cov=app --cov-fail-under=90
   behave tests/behavioral/
   ```

3. **Verify code quality**
   ```bash
   pre-commit run --all-files
   ```

### PR Requirements

- **Clear title** following conventional commits
- **Detailed description** with context and rationale
- **Linked issues** using GitHub keywords
- **Test evidence** - screenshots, test output, or performance metrics
- **Documentation updates** - README, API docs, or architectural decisions

### Review Process

1. **Automated Checks** - All CI checks must pass
2. **Code Review** - At least one maintainer approval required
3. **Architecture Review** - For significant changes affecting system design
4. **Performance Review** - For changes affecting API response times or database queries

## 🏆 Recognition

Contributors are recognised in several ways:

- **Contributors List** - All contributors listed in README
- **Release Notes** - Significant contributions highlighted in changelog
- **GitHub Achievements** - Contribution badges and statistics
- **Community Recognition** - Shout-outs in project communications

## 📞 Getting Help

- **GitHub Discussions** - General questions and feature discussions
- **GitHub Issues** - Bug reports and specific technical questions
- **Email** - [<EMAIL>](mailto:<EMAIL>) for sensitive matters

## 📜 Code of Conduct

This project adheres to a code of conduct that ensures a welcoming environment for all contributors. Key principles:

- **Respectful Communication** - Professional, constructive feedback
- **Inclusive Environment** - Welcome contributors of all backgrounds and experience levels
- **Scientific Rigour** - Evidence-based discussions and decisions
- **Collaborative Spirit** - Work together towards common goals

---

Thank you for contributing to the advancement of evidence-based training technology! 🏋️‍♂️

*This document follows British English conventions and is maintained by the RP Training development team.*
